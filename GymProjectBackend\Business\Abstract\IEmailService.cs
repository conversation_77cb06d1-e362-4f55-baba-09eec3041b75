using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IEmailService
    {
        Task<IResult> SendPasswordResetEmailAsync(string toEmail, string resetToken, string userName);
        Task<IResult> SendWelcomeEmailAsync(string toEmail, string userName, string tempPassword);
        Task<IResult> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true);
        Task<IResult> SendPasswordChangedNotificationAsync(string toEmail, string userName);
    }
}
