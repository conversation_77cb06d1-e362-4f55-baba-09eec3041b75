using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IPasswordResetService
    {
        Task<IResult> RequestPasswordResetAsync(PasswordResetRequestDto request);
        Task<IResult> ValidateResetTokenAsync(PasswordResetValidationDto request);
        Task<IResult> ResetPasswordAsync(PasswordResetDto request);
        IResult CleanExpiredTokens();
    }
}
