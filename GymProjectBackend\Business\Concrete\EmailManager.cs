using Business.Abstract;
using Core.Utilities.Mail;
using Core.Utilities.Results;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Configuration;
using MimeKit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class EmailManager : IEmailService
    {
        private readonly EmailSettings _emailSettings;

        public EmailManager(IConfiguration configuration)
        {
            var environment = configuration["Environment"] ?? "dev";
            _emailSettings = configuration.GetSection($"EmailSettings:{environment}").Get<EmailSettings>();
        }

        public async Task<IResult> SendPasswordResetEmailAsync(string toEmail, string resetToken, string userName)
        {
            try
            {
                var resetUrl = $"{_emailSettings.ResetPasswordUrl}?token={resetToken}";
                var subject = "Şifre Sıfırlama Talebi - GymKod";
                
                var body = GetPasswordResetEmailTemplate(userName, resetUrl);
                
                return await SendEmailAsync(toEmail, subject, body, true);
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Şifre sıfırlama e-postası gönderilemedi: {ex.Message}");
            }
        }

        public async Task<IResult> SendWelcomeEmailAsync(string toEmail, string userName, string tempPassword)
        {
            try
            {
                var subject = "GymKod Sistemi'ne Hoş Geldiniz!";
                var body = GetWelcomeEmailTemplate(userName, tempPassword);
                
                return await SendEmailAsync(toEmail, subject, body, true);
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Hoş geldin e-postası gönderilemedi: {ex.Message}");
            }
        }

        public async Task<IResult> SendPasswordChangedNotificationAsync(string toEmail, string userName)
        {
            try
            {
                var subject = "Şifreniz Başarıyla Değiştirildi - GymKod";
                var body = GetPasswordChangedEmailTemplate(userName);
                
                return await SendEmailAsync(toEmail, subject, body, true);
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Şifre değişikliği bildirimi gönderilemedi: {ex.Message}");
            }
        }

        public async Task<IResult> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(_emailSettings.FromName, _emailSettings.FromEmail));
                message.To.Add(new MailboxAddress("", toEmail));
                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, 
                    _emailSettings.EnableSsl ? SecureSocketOptions.StartTls : SecureSocketOptions.None);
                
                await client.AuthenticateAsync(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                return new SuccessResult("E-posta başarıyla gönderildi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"E-posta gönderilemedi: {ex.Message}");
            }
        }

        private string GetPasswordResetEmailTemplate(string userName, string resetUrl)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .button {{ display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>GymKod Sistemi</h1>
        </div>
        <div class='content'>
            <h2>Merhaba {userName},</h2>
            <p>Şifre sıfırlama talebiniz alınmıştır. Aşağıdaki bağlantıya tıklayarak yeni şifrenizi belirleyebilirsiniz:</p>
            <p style='text-align: center;'>
                <a href='{resetUrl}' class='button'>Şifremi Sıfırla</a>
            </p>
            <p><strong>Önemli:</strong> Bu bağlantı 24 saat geçerlidir. Eğer şifre sıfırlama talebinde bulunmadıysanız, bu e-postayı dikkate almayın.</p>
            <p>Güvenliğiniz için şifrenizi kimseyle paylaşmayın.</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 GymKod Sistemi. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetWelcomeEmailTemplate(string userName, string tempPassword)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #28a745; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .credentials {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>GymKod Sistemi'ne Hoş Geldiniz!</h1>
        </div>
        <div class='content'>
            <h2>Merhaba {userName},</h2>
            <p>GymKod sistemi hesabınız başarıyla oluşturulmuştur. Sisteme giriş yapmak için aşağıdaki bilgileri kullanabilirsiniz:</p>
            <div class='credentials'>
                <p><strong>Geçici Şifreniz:</strong> {tempPassword}</p>
            </div>
            <p><strong>Önemli:</strong> Güvenliğiniz için ilk girişinizde şifrenizi değiştirmeniz gerekmektedir.</p>
            <p>Sisteme giriş yaptıktan sonra profil ayarlarınızdan şifrenizi güncelleyebilirsiniz.</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 GymKod Sistemi. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordChangedEmailTemplate(string userName)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #28a745; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Şifre Değişikliği Onayı</h1>
        </div>
        <div class='content'>
            <h2>Merhaba {userName},</h2>
            <p>GymKod sistemi şifreniz başarıyla değiştirilmiştir.</p>
            <p><strong>Değişiklik Zamanı:</strong> {DateTime.Now:dd.MM.yyyy HH:mm}</p>
            <p>Eğer bu değişikliği siz yapmadıysanız, lütfen derhal bizimle iletişime geçin.</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 GymKod Sistemi. Tüm hakları saklıdır.</p>
        </div>
    </div>
</body>
</html>";
        }
    }
}
