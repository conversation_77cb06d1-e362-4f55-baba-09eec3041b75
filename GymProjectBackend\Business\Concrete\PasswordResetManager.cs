using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class PasswordResetManager : IPasswordResetService
    {
        private readonly IPasswordResetTokenDal _passwordResetTokenDal;
        private readonly IUserService _userService;
        private readonly IEmailService _emailService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PasswordResetManager(
            IPasswordResetTokenDal passwordResetTokenDal,
            IUserService userService,
            IEmailService emailService,
            IHttpContextAccessor httpContextAccessor)
        {
            _passwordResetTokenDal = passwordResetTokenDal;
            _userService = userService;
            _emailService = emailService;
            _httpContextAccessor = httpContextAccessor;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public async Task<IResult> RequestPasswordResetAsync(PasswordResetRequestDto request)
        {
            try
            {
                // Kullanıcıyı e-posta ile bul
                var user = _userService.GetByMail(request.Email);
                if (user == null)
                {
                    // Güvenlik için kullanıcı bulunamasa bile başarılı mesajı döndür
                    return new SuccessResult("Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama bağlantısı gönderilmiştir.");
                }

                // Kullanıcının mevcut token'larını geçersiz kıl
                _passwordResetTokenDal.InvalidateUserTokens(user.UserID);

                // Yeni token oluştur
                var token = GenerateSecureToken();
                var expiryDate = DateTime.Now.AddHours(24); // 24 saat geçerli

                var resetToken = new PasswordResetToken
                {
                    UserId = user.UserID,
                    Token = token,
                    ExpiryDate = expiryDate,
                    IsUsed = false,
                    CreatedDate = DateTime.Now,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent()
                };

                _passwordResetTokenDal.Add(resetToken);

                // E-posta gönder
                var emailResult = await _emailService.SendPasswordResetEmailAsync(
                    user.Email, 
                    token, 
                    $"{user.FirstName} {user.LastName}");

                if (!emailResult.Success)
                {
                    return new ErrorResult("Şifre sıfırlama e-postası gönderilemedi. Lütfen daha sonra tekrar deneyin.");
                }

                return new SuccessResult("Şifre sıfırlama bağlantısı e-posta adresinize gönderilmiştir.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Şifre sıfırlama talebi işlenirken hata oluştu: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public async Task<IResult> ValidateResetTokenAsync(PasswordResetValidationDto request)
        {
            try
            {
                var token = _passwordResetTokenDal.GetValidToken(request.Token);
                if (token == null)
                {
                    return new ErrorResult("Geçersiz veya süresi dolmuş token.");
                }

                return new SuccessResult("Token geçerli.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token doğrulama hatası: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public async Task<IResult> ResetPasswordAsync(PasswordResetDto request)
        {
            try
            {
                // Token'ı doğrula
                var token = _passwordResetTokenDal.GetValidToken(request.Token);
                if (token == null)
                {
                    return new ErrorResult("Geçersiz veya süresi dolmuş token.");
                }

                // Şifre doğrulama
                if (request.NewPassword != request.ConfirmPassword)
                {
                    return new ErrorResult("Şifreler eşleşmiyor.");
                }

                if (string.IsNullOrWhiteSpace(request.NewPassword) || request.NewPassword.Length < 6)
                {
                    return new ErrorResult("Şifre en az 6 karakter olmalıdır.");
                }

                // Kullanıcıyı al
                var userResult = _userService.GetById(token.UserId);
                if (!userResult.Success)
                {
                    return new ErrorResult("Kullanıcı bulunamadı.");
                }

                var user = userResult.Data;

                // Yeni şifre hash'le
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(request.NewPassword, out passwordHash, out passwordSalt);

                // Kullanıcı şifresini güncelle
                user.PasswordHash = passwordHash;
                user.PasswordSalt = passwordSalt;
                user.RequirePasswordChange = false;
                user.UpdatedDate = DateTime.Now;

                var updateResult = _userService.Update(user);
                if (!updateResult.Success)
                {
                    return new ErrorResult("Şifre güncellenirken hata oluştu.");
                }

                // Token'ı kullanılmış olarak işaretle
                token.IsUsed = true;
                token.UsedDate = DateTime.Now;
                _passwordResetTokenDal.Update(token);

                // Kullanıcının diğer token'larını da geçersiz kıl
                _passwordResetTokenDal.InvalidateUserTokens(user.UserID);

                // Şifre değişikliği bildirimi gönder
                await _emailService.SendPasswordChangedNotificationAsync(
                    user.Email, 
                    $"{user.FirstName} {user.LastName}");

                return new SuccessResult("Şifreniz başarıyla değiştirilmiştir.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Şifre sıfırlama işlemi başarısız: {ex.Message}");
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public IResult CleanExpiredTokens()
        {
            try
            {
                _passwordResetTokenDal.CleanExpiredTokens();
                return new SuccessResult("Süresi dolmuş token'lar temizlendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token temizleme hatası: {ex.Message}");
            }
        }

        private string GenerateSecureToken()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        private string GetClientIpAddress()
        {
            return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private string GetUserAgent()
        {
            return _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString() ?? "Unknown";
        }
    }
}
