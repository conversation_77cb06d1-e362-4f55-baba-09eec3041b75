using Core.DataAccess;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IPasswordResetTokenDal : IEntityRepository<PasswordResetToken>
    {
        PasswordResetToken GetValidToken(string token);
        void InvalidateUserTokens(int userId);
        void CleanExpiredTokens();
    }
}
