using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfPasswordResetTokenDal : EfEntityRepositoryBase<PasswordResetToken, GymContext>, IPasswordResetTokenDal
    {
        // Constructor injection (Scalability için)
        public EfPasswordResetTokenDal(GymContext context) : base(context)
        {
        }

        public PasswordResetToken GetValidToken(string token)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.PasswordResetTokens
                .FirstOrDefault(t => t.Token == token && 
                               !t.IsUsed && 
                               t.ExpiryDate > DateTime.Now);
        }

        public void InvalidateUserTokens(int userId)
        {
            // DI kullanılıyor - Scalability optimized
            var userTokens = _context.PasswordResetTokens
                .Where(t => t.UserId == userId && !t.IsUsed)
                .ToList();

            foreach (var token in userTokens)
            {
                token.IsUsed = true;
                token.UsedDate = DateTime.Now;
            }

            _context.SaveChanges();
        }

        public void CleanExpiredTokens()
        {
            // DI kullanılıyor - Scalability optimized
            var expiredTokens = _context.PasswordResetTokens
                .Where(t => t.ExpiryDate < DateTime.Now && !t.IsUsed)
                .ToList();

            foreach (var token in expiredTokens)
            {
                token.IsUsed = true;
                token.UsedDate = DateTime.Now;
            }

            _context.SaveChanges();
        }
    }
}
