-- PasswordResetToken tablosu oluşturma migration'ı
-- <PERSON>u script'i SQL Server Management Studio'da çalıştırın

-- Tablo var mı kontrol et, yoksa oluştur
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PasswordResetTokens' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PasswordResetTokens](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [Token] [nvarchar](255) NOT NULL,
        [ExpiryDate] [datetime2](7) NOT NULL,
        [IsUsed] [bit] NOT NULL DEFAULT 0,
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT GETDATE(),
        [UsedDate] [datetime2](7) NULL,
        [IpAddress] [nvarchar](50) NULL,
        [UserAgent] [nvarchar](500) NULL,
        CONSTRAINT [PK_PasswordResetTokens] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_PasswordResetTokens_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserID])
    )

    -- İndeksler oluştur (Performance için kritik)
    CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_Token] ON [dbo].[PasswordResetTokens] ([Token])
    CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_UserId] ON [dbo].[PasswordResetTokens] ([UserId])
    CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_ExpiryDate] ON [dbo].[PasswordResetTokens] ([ExpiryDate])
    CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_IsUsed] ON [dbo].[PasswordResetTokens] ([IsUsed])

    PRINT 'PasswordResetTokens tablosu başarıyla oluşturuldu.'
END
ELSE
BEGIN
    PRINT 'PasswordResetTokens tablosu zaten mevcut.'
END

-- Temizlik stored procedure'ü oluştur
IF NOT EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'CleanExpiredPasswordResetTokens')
BEGIN
    EXEC('
    CREATE PROCEDURE [dbo].[CleanExpiredPasswordResetTokens]
    AS
    BEGIN
        SET NOCOUNT ON;
        
        -- 30 günden eski token''ları sil (performans için)
        DELETE FROM [dbo].[PasswordResetTokens] 
        WHERE [CreatedDate] < DATEADD(day, -30, GETDATE())
        
        -- Kullanılmış ve süresi dolmuş token''ları işaretle
        UPDATE [dbo].[PasswordResetTokens] 
        SET [IsUsed] = 1, [UsedDate] = GETDATE()
        WHERE [ExpiryDate] < GETDATE() AND [IsUsed] = 0
        
        PRINT ''Süresi dolmuş token''lar temizlendi.''
    END')
    
    PRINT 'CleanExpiredPasswordResetTokens stored procedure''ü oluşturuldu.'
END

-- Otomatik temizlik için SQL Server Agent Job oluşturma (opsiyonel)
-- Bu kısmı production'da çalıştırabilirsiniz
/*
USE [msdb]
GO

IF NOT EXISTS (SELECT job_id FROM msdb.dbo.sysjobs WHERE name = 'GymProject_CleanExpiredTokens')
BEGIN
    EXEC dbo.sp_add_job
        @job_name = N'GymProject_CleanExpiredTokens',
        @enabled = 1,
        @description = N'Süresi dolmuş şifre sıfırlama token''larını temizler'

    EXEC dbo.sp_add_jobstep
        @job_name = N'GymProject_CleanExpiredTokens',
        @step_name = N'Clean Tokens',
        @command = N'EXEC [GymProject].[dbo].[CleanExpiredPasswordResetTokens]',
        @database_name = N'GymProject'

    EXEC dbo.sp_add_schedule
        @schedule_name = N'Daily_Cleanup',
        @freq_type = 4,
        @freq_interval = 1,
        @active_start_time = 020000

    EXEC dbo.sp_attach_schedule
        @job_name = N'GymProject_CleanExpiredTokens',
        @schedule_name = N'Daily_Cleanup'

    EXEC dbo.sp_add_jobserver
        @job_name = N'GymProject_CleanExpiredTokens'
        
    PRINT 'Otomatik temizlik job''ı oluşturuldu.'
END
*/
