using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class PasswordResetRequestDto : IDto
    {
        public string Email { get; set; }
    }

    public class PasswordResetDto : IDto
    {
        public string Token { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }
    }

    public class PasswordResetValidationDto : IDto
    {
        public string Token { get; set; }
    }
}
