/* Mevcut login.component.css stillerini kullan ve ek stiller ekle */
@import '../login/login.component.css';

.forgot-password-form {
  width: 100%;
}

.email-sent-message {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  margin-bottom: 1.5rem;
}

.success-icon i {
  font-size: 4rem;
  color: var(--success-color, #28a745);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.email-sent-message h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.email-sent-message p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.instructions {
  margin: 2rem 0;
  text-align: left;
}

.instruction-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: var(--surface-color);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.instruction-item i {
  color: var(--primary-color);
  margin-right: 1rem;
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.instruction-item span {
  color: var(--text-primary);
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.resend-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.resend-button:hover {
  background: var(--secondary-hover);
  transform: translateY(-2px);
}

.back-to-login {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
}

.back-to-login:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Dark mode support */
[data-theme="dark"] .instruction-item {
  background: var(--surface-dark);
  border-left-color: var(--primary-light);
}

[data-theme="dark"] .instruction-item i {
  color: var(--primary-light);
}

[data-theme="dark"] .instruction-item span {
  color: var(--text-primary-dark);
}

[data-theme="dark"] .success-icon i {
  color: var(--success-light);
}

/* Responsive design */
@media (max-width: 768px) {
  .action-buttons {
    gap: 0.75rem;
  }
  
  .resend-button,
  .back-to-login {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .success-icon i {
    font-size: 3rem;
  }
  
  .email-sent-message h2 {
    font-size: 1.5rem;
  }
  
  .instruction-item {
    padding: 0.6rem;
  }
  
  .instruction-item i {
    font-size: 1rem;
  }
}
