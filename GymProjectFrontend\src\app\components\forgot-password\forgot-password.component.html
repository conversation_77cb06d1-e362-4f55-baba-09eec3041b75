<div class="login-container">
  <div class="login-wrapper">
    <!-- Left Panel with Gym Image -->
    <div class="login-image-panel">
      <div class="overlay">
        <div class="gym-branding">
          <div class="logo-container">
            <i class="fas fa-dumbbell"></i>
          </div>
          <h1>GymKod Pro</h1>
          <p>Profesyonel Spor Salonu Yönetim Sistemi</p>
          <div class="features">
            <div class="feature">
              <i class="fas fa-shield-alt"></i>
              <span>Güvenli Giriş</span>
            </div>
            <div class="feature">
              <i class="fas fa-envelope"></i>
              <span>E-posta Doğrulama</span>
            </div>
            <div class="feature">
              <i class="fas fa-lock"></i>
              <span>Şifre Koruması</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Panel with Forgot Password Form -->
    <div class="login-form-panel">
      <div class="login-form-container">
        
        <!-- Email Form -->
        <div *ngIf="!isEmailSent" class="forgot-password-form">
          <div class="login-header">
            <div class="header-icon">
              <i class="fas fa-key"></i>
            </div>
            <h2>Şifremi Unuttum</h2>
            <p>E-posta adresinizi girin, size şifre sıfırlama bağlantısı gönderelim</p>
          </div>
          
          <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="login-form">
            <div class="form-group">
              <label for="email">E-posta Adresi</label>
              <div class="input-group">
                <i class="fas fa-envelope"></i>
                <input 
                  id="email"
                  type="email" 
                  formControlName="email" 
                  placeholder="E-posta adresinizi girin"
                  [ngClass]="{'is-invalid': forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched}"
                >
              </div>
              <div class="error-message" *ngIf="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched">
                <small *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">E-posta adresi gerekli</small>
                <small *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">Geçerli bir e-posta adresi girin</small>
              </div>
            </div>

            <button 
              type="submit" 
              [disabled]="forgotPasswordForm.invalid || isLoading"
              class="login-button"
            >
              <span *ngIf="!isLoading">Şifre Sıfırlama Bağlantısı Gönder</span>
              <app-loading-spinner *ngIf="isLoading" [size]="'small'" [showText]="false"></app-loading-spinner>
            </button>
          </form>
          
          <div class="form-actions">
            <button type="button" class="back-to-login" (click)="goToLogin()">
              <i class="fas fa-arrow-left"></i>
              Giriş sayfasına dön
            </button>
          </div>
        </div>

        <!-- Success Message -->
        <div *ngIf="isEmailSent" class="email-sent-message">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h2>E-posta Gönderildi!</h2>
          <p>Şifre sıfırlama bağlantısı e-posta adresinize gönderilmiştir.</p>
          <div class="instructions">
            <div class="instruction-item">
              <i class="fas fa-envelope-open"></i>
              <span>E-posta kutunuzu kontrol edin</span>
            </div>
            <div class="instruction-item">
              <i class="fas fa-clock"></i>
              <span>Bağlantı 24 saat geçerlidir</span>
            </div>
            <div class="instruction-item">
              <i class="fas fa-spam"></i>
              <span>Spam klasörünü de kontrol edin</span>
            </div>
          </div>
          
          <div class="action-buttons">
            <button type="button" class="resend-button" (click)="resendEmail()">
              <i class="fas fa-redo"></i>
              Tekrar Gönder
            </button>
            <button type="button" class="back-to-login" (click)="goToLogin()">
              <i class="fas fa-arrow-left"></i>
              Giriş sayfasına dön
            </button>
          </div>
        </div>
        
        <div class="login-footer">
          <div class="support">
            <i class="fas fa-headset"></i>
            <span>Destek: <a href="mailto:support&#64;gymkod.com">support&#64;gymkod.com</a></span>
          </div>
          <p>© 2025 GymKod Pro. Tüm hakları saklıdır.</p>
        </div>
      </div>
    </div>
  </div>
</div>
