import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-forgot-password',
  standalone: false,
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm: FormGroup;
  isLoading = false;
  isEmailSent = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private toastrService: ToastrService
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  ngOnInit(): void {
    // <PERSON><PERSON><PERSON> kullan<PERSON><PERSON> zaten giriş ya<PERSON>, uygun say<PERSON>ya yönlendir
    if (this.authService.isAuthenticated()) {
      if (this.authService.hasRole('owner')) {
        this.router.navigate(['/license-dashboard']);
      } else if (this.authService.hasRole('admin')) {
        this.router.navigate(['/todayentries']);
      } else if (this.authService.hasRole('member')) {
        this.router.navigate(['/my-qr']);
      }
    }
  }

  async onSubmit(): Promise<void> {
    if (this.forgotPasswordForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;

    try {
      const email = this.forgotPasswordForm.get('email')?.value;
      
      const response = await this.authService.forgotPassword(email).toPromise();
      
      if (response?.success) {
        this.isEmailSent = true;
        this.toastrService.success(response.message, 'Başarılı');
      } else {
        this.toastrService.error(response?.message || 'Bir hata oluştu', 'Hata');
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      this.toastrService.error(
        error?.message || 'Şifre sıfırlama talebi gönderilemedi. Lütfen tekrar deneyin.',
        'Hata'
      );
    } finally {
      this.isLoading = false;
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.forgotPasswordForm.controls).forEach(key => {
      const control = this.forgotPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  resendEmail(): void {
    this.isEmailSent = false;
    this.onSubmit();
  }
}
