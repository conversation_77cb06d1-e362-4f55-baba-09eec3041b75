/* Mevcut login.component.css stillerini kullan ve ek stiller ekle */
@import '../login/login.component.css';

.token-checking {
  text-align: center;
  padding: 3rem 0;
}

.loading-icon {
  margin-bottom: 2rem;
}

.invalid-token {
  text-align: center;
  padding: 2rem 0;
}

.error-icon {
  margin-bottom: 1.5rem;
}

.error-icon i {
  font-size: 4rem;
  color: var(--error-color, #dc3545);
  animation: errorPulse 2s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.invalid-token h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.invalid-token p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.reset-password-form {
  width: 100%;
}

.password-strength {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  width: 33%;
  background: var(--error-color, #dc3545);
}

.strength-fill.medium {
  width: 66%;
  background: var(--warning-color, #ffc107);
}

.strength-fill.strong {
  width: 100%;
  background: var(--success-color, #28a745);
}

.strength-text {
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 50px;
}

.strength-text.weak {
  color: var(--error-color, #dc3545);
}

.strength-text.medium {
  color: var(--warning-color, #ffc107);
}

.strength-text.strong {
  color: var(--success-color, #28a745);
}

.password-requirements {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: var(--surface-color);
  border-radius: 6px;
  border-left: 3px solid var(--info-color, #17a2b8);
}

.password-requirements small {
  color: var(--text-secondary);
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.password-requirements ul {
  margin: 0;
  padding-left: 1.2rem;
  list-style-type: disc;
}

.password-requirements li {
  color: var(--text-secondary);
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.primary-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.primary-button:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
}

.back-to-login {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
}

.back-to-login:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Dark mode support */
[data-theme="dark"] .password-requirements {
  background: var(--surface-dark);
  border-left-color: var(--info-light);
}

[data-theme="dark"] .password-requirements small,
[data-theme="dark"] .password-requirements li {
  color: var(--text-secondary-dark);
}

[data-theme="dark"] .strength-bar {
  background: var(--border-dark);
}

[data-theme="dark"] .error-icon i {
  color: var(--error-light);
}

/* Responsive design */
@media (max-width: 768px) {
  .action-buttons {
    gap: 0.75rem;
  }
  
  .primary-button,
  .back-to-login {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .error-icon i {
    font-size: 3rem;
  }
  
  .invalid-token h2 {
    font-size: 1.5rem;
  }
  
  .password-requirements {
    padding: 0.6rem;
  }
  
  .password-strength {
    gap: 0.5rem;
  }
  
  .strength-text {
    min-width: 40px;
    font-size: 0.75rem;
  }
}
