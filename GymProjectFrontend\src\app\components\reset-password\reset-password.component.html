<div class="login-container">
  <div class="login-wrapper">
    <!-- Left Panel with Gym Image -->
    <div class="login-image-panel">
      <div class="overlay">
        <div class="gym-branding">
          <div class="logo-container">
            <i class="fas fa-dumbbell"></i>
          </div>
          <h1>GymKod Pro</h1>
          <p>Profesyonel Spor Salonu Yönetim Sistemi</p>
          <div class="features">
            <div class="feature">
              <i class="fas fa-shield-alt"></i>
              <span>Güvenli Şifre</span>
            </div>
            <div class="feature">
              <i class="fas fa-lock"></i>
              <span>Şifre Koruması</span>
            </div>
            <div class="feature">
              <i class="fas fa-check-circle"></i>
              <span>Doğrulanmış</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Panel with Reset Password Form -->
    <div class="login-form-panel">
      <div class="login-form-container">
        
        <!-- Token Checking -->
        <div *ngIf="isTokenChecking" class="token-checking">
          <div class="loading-icon">
            <app-loading-spinner [size]="'large'" [showText]="true" [text]="'Token doğrulanıyor...'"></app-loading-spinner>
          </div>
        </div>

        <!-- Invalid Token -->
        <div *ngIf="!isTokenChecking && !isTokenValid" class="invalid-token">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h2>Geçersiz Bağlantı</h2>
          <p>Bu şifre sıfırlama bağlantısı geçersiz veya süresi dolmuş.</p>
          
          <div class="action-buttons">
            <button type="button" class="primary-button" (click)="requestNewToken()">
              <i class="fas fa-redo"></i>
              Yeni Bağlantı Talep Et
            </button>
            <button type="button" class="back-to-login" (click)="goToLogin()">
              <i class="fas fa-arrow-left"></i>
              Giriş sayfasına dön
            </button>
          </div>
        </div>

        <!-- Reset Password Form -->
        <div *ngIf="!isTokenChecking && isTokenValid" class="reset-password-form">
          <div class="login-header">
            <div class="header-icon">
              <i class="fas fa-key"></i>
            </div>
            <h2>Yeni Şifre Belirle</h2>
            <p>Güvenli bir şifre oluşturun</p>
          </div>
          
          <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="login-form">
            <div class="form-group">
              <label for="newPassword">Yeni Şifre</label>
              <div class="input-group">
                <i class="fas fa-lock"></i>
                <input 
                  id="newPassword"
                  [type]="passwordVisible ? 'text' : 'password'"
                  formControlName="newPassword"
                  placeholder="Yeni şifrenizi girin"
                  [ngClass]="{'is-invalid': resetPasswordForm.get('newPassword')?.invalid && resetPasswordForm.get('newPassword')?.touched}"
                >
                <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                  <i class="fas" [class.fa-eye-slash]="passwordVisible" [class.fa-eye]="!passwordVisible"></i>
                </button>
              </div>
              
              <!-- Password Strength Indicator -->
              <div class="password-strength" *ngIf="resetPasswordForm.get('newPassword')?.value">
                <div class="strength-bar">
                  <div class="strength-fill" [ngClass]="getPasswordStrength()"></div>
                </div>
                <span class="strength-text" [ngClass]="getPasswordStrength()">
                  {{ getPasswordStrengthText() }}
                </span>
              </div>
              
              <div class="error-message" *ngIf="resetPasswordForm.get('newPassword')?.invalid && resetPasswordForm.get('newPassword')?.touched">
                <small *ngIf="resetPasswordForm.get('newPassword')?.errors?.['required']">Yeni şifre gerekli</small>
                <small *ngIf="resetPasswordForm.get('newPassword')?.errors?.['minlength']">Şifre en az 6 karakter olmalı</small>
              </div>
              
              <div class="password-requirements">
                <small>Şifre gereksinimleri:</small>
                <ul>
                  <li>En az 6 karakter</li>
                  <li>Güçlü şifre için: büyük harf, küçük harf, rakam ve özel karakter kullanın</li>
                </ul>
              </div>
            </div>

            <div class="form-group">
              <label for="confirmPassword">Şifre Tekrarı</label>
              <div class="input-group">
                <i class="fas fa-lock"></i>
                <input 
                  id="confirmPassword"
                  [type]="confirmPasswordVisible ? 'text' : 'password'"
                  formControlName="confirmPassword"
                  placeholder="Şifrenizi tekrar girin"
                  [ngClass]="{'is-invalid': resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched}"
                >
                <button type="button" class="toggle-password" (click)="toggleConfirmPasswordVisibility()">
                  <i class="fas" [class.fa-eye-slash]="confirmPasswordVisible" [class.fa-eye]="!confirmPasswordVisible"></i>
                </button>
              </div>
              <div class="error-message" *ngIf="resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched">
                <small *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['required']">Şifre tekrarı gerekli</small>
                <small *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['passwordMismatch']">Şifreler eşleşmiyor</small>
              </div>
            </div>

            <button 
              type="submit" 
              [disabled]="resetPasswordForm.invalid || isLoading"
              class="login-button"
            >
              <span *ngIf="!isLoading">Şifremi Değiştir</span>
              <app-loading-spinner *ngIf="isLoading" [size]="'small'" [showText]="false"></app-loading-spinner>
            </button>
          </form>
          
          <div class="form-actions">
            <button type="button" class="back-to-login" (click)="goToLogin()">
              <i class="fas fa-arrow-left"></i>
              Giriş sayfasına dön
            </button>
          </div>
        </div>
        
        <div class="login-footer">
          <div class="support">
            <i class="fas fa-headset"></i>
            <span>Destek: <a href="mailto:support&#64;gymkod.com">support&#64;gymkod.com</a></span>
          </div>
          <p>© 2025 GymKod Pro. Tüm hakları saklıdır.</p>
        </div>
      </div>
    </div>
  </div>
</div>
