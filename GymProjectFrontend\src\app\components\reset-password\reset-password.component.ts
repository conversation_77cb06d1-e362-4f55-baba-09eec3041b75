import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reset-password',
  standalone: false,
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css']
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm: FormGroup;
  isLoading = false;
  isTokenValid = false;
  isTokenChecking = true;
  token: string = '';
  passwordVisible = false;
  confirmPasswordVisible = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private toastrService: ToastrService
  ) {
    this.resetPasswordForm = this.formBuilder.group({
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    // URL'den token'ı al
    this.route.queryParams.subscribe(params => {
      this.token = params['token'];
      if (this.token) {
        this.validateToken();
      } else {
        this.isTokenChecking = false;
        this.isTokenValid = false;
        this.toastrService.error('Geçersiz şifre sıfırlama bağlantısı', 'Hata');
      }
    });
  }

  private async validateToken(): Promise<void> {
    try {
      const response = await this.authService.validateResetToken(this.token).toPromise();
      
      if (response?.success) {
        this.isTokenValid = true;
      } else {
        this.isTokenValid = false;
        this.toastrService.error(response?.message || 'Token geçersiz veya süresi dolmuş', 'Hata');
      }
    } catch (error: any) {
      console.error('Token validation error:', error);
      this.isTokenValid = false;
      this.toastrService.error('Token doğrulama hatası', 'Hata');
    } finally {
      this.isTokenChecking = false;
    }
  }

  private passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
    
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  async onSubmit(): Promise<void> {
    if (this.resetPasswordForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;

    try {
      const formValue = this.resetPasswordForm.value;
      
      const resetData = {
        token: this.token,
        newPassword: formValue.newPassword,
        confirmPassword: formValue.confirmPassword
      };
      
      const response = await this.authService.resetPassword(resetData).toPromise();
      
      if (response?.success) {
        this.toastrService.success(response.message, 'Başarılı');
        // 2 saniye bekle ve login sayfasına yönlendir
        setTimeout(() => {
          this.router.navigate(['/login']);
        }, 2000);
      } else {
        this.toastrService.error(response?.message || 'Şifre sıfırlama başarısız', 'Hata');
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      this.toastrService.error(
        error?.message || 'Şifre sıfırlama işlemi başarısız. Lütfen tekrar deneyin.',
        'Hata'
      );
    } finally {
      this.isLoading = false;
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.resetPasswordForm.controls).forEach(key => {
      const control = this.resetPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  requestNewToken(): void {
    this.router.navigate(['/forgot-password']);
  }

  getPasswordStrength(): string {
    const password = this.resetPasswordForm.get('newPassword')?.value || '';

    if (password.length === 0) return '';
    if (password.length < 6) return 'weak';
    if (password.length < 8) return 'medium';

    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const strength = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar].filter(Boolean).length;

    if (strength >= 3 && password.length >= 8) return 'strong';
    if (strength >= 2) return 'medium';
    return 'weak';
  }

  getPasswordStrengthText(): string {
    const strength = this.getPasswordStrength();
    switch (strength) {
      case 'weak': return 'Zayıf';
      case 'medium': return 'Orta';
      case 'strong': return 'Güçlü';
      default: return '';
    }
  }
}
